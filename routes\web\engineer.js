/**
 * Engineer Web Routes
 * Handles engineer dashboard and management pages
 */

const express = require('express');
const router = express.Router();

// Middleware
const { requireAuth, requireEngineerOrAdmin } = require('../../middleware/auth');

// Apply authentication and engineer role check to all routes
router.use(requireAuth);
router.use(requireEngineerOrAdmin);

/**
 * @route   GET /engineer/dashboard
 * @desc    Render engineer dashboard
 * @access  Private (Engineer only)
 */
router.get('/dashboard',
  (req, res) => {
    res.render('engineer/dashboard', {
      title: 'لوحة التحكم - Decore & More',
      user: req.user,
      layout: 'layouts/engineer'
    });
  }
);

/**
 * @route   GET /engineer/profile
 * @desc    Render engineer profile management
 * @access  Private (Engineer only)
 */
router.get('/profile',
  (req, res) => {
    res.render('engineer/profile', {
      title: 'إدارة الملف الشخصي - Decore & More',
      user: req.user,
      layout: 'layouts/engineer'
    });
  }
);

/**
 * @route   GET /engineer/projects
 * @desc    Render engineer projects management
 * @access  Private (Engineer only)
 */
router.get('/projects',
  (req, res) => {
    res.render('engineer/projects', {
      title: 'إدارة المشاريع - Decore & More',
      user: req.user,
      layout: 'layouts/engineer'
    });
  }
);

/**
 * @route   GET /engineer/projects/create
 * @desc    Render create project page
 * @access  Private (Engineer only)
 */
router.get('/projects/create',
  (req, res) => {
    res.render('engineer/create-project', {
      title: 'إضافة مشروع جديد - Decore & More',
      user: req.user,
      layout: 'layouts/engineer'
    });
  }
);

/**
 * @route   GET /engineer/projects/edit/:projectId
 * @desc    Render edit project page
 * @access  Private (Engineer only)
 */
router.get('/projects/edit/:projectId',
  (req, res) => {
    const { projectId } = req.params;
    
    res.render('engineer/edit-project', {
      title: 'تعديل المشروع - Decore & More',
      projectId,
      user: req.user,
      layout: 'layouts/engineer'
    });
  }
);

/**
 * @route   GET /engineer/packages
 * @desc    Render engineer packages management
 * @access  Private (Engineer only)
 */
router.get('/packages',
  (req, res) => {
    res.render('engineer/packages', {
      title: 'إدارة الباقات - Decore & More',
      user: req.user,
      layout: 'layouts/engineer'
    });
  }
);

/**
 * @route   GET /engineer/packages/create
 * @desc    Render create package page
 * @access  Private (Engineer only)
 */
router.get('/packages/create',
  (req, res) => {
    res.render('engineer/create-package', {
      title: 'إضافة باقة جديدة - Decore & More',
      user: req.user,
      layout: 'layouts/engineer'
    });
  }
);

/**
 * @route   GET /engineer/packages/edit/:packageId
 * @desc    Render edit package page
 * @access  Private (Engineer only)
 */
router.get('/packages/edit/:packageId',
  (req, res) => {
    const { packageId } = req.params;
    
    res.render('engineer/edit-package', {
      title: 'تعديل الباقة - Decore & More',
      packageId,
      user: req.user,
      layout: 'layouts/engineer'
    });
  }
);

/**
 * @route   GET /engineer/bookings
 * @desc    Render engineer bookings management
 * @access  Private (Engineer only)
 */
router.get('/bookings',
  (req, res) => {
    res.render('engineer/bookings', {
      title: 'إدارة الحجوزات - Decore & More',
      user: req.user,
      layout: 'layouts/engineer'
    });
  }
);

/**
 * @route   GET /engineer/messages
 * @desc    Render engineer messages
 * @access  Private (Engineer only)
 */
router.get('/messages',
  (req, res) => {
    res.render('engineer/messages', {
      title: 'الرسائل - Decore & More',
      user: req.user,
      layout: 'layouts/engineer'
    });
  }
);

/**
 * @route   GET /engineer/earnings
 * @desc    Render engineer earnings page
 * @access  Private (Engineer only)
 */
router.get('/earnings',
  (req, res) => {
    res.render('engineer/earnings', {
      title: 'الأرباح - Decore & More',
      user: req.user,
      layout: 'layouts/engineer'
    });
  }
);

/**
 * @route   GET /engineer/analytics
 * @desc    Render engineer analytics page
 * @access  Private (Engineer only)
 */
router.get('/analytics',
  (req, res) => {
    res.render('engineer/analytics', {
      title: 'التحليلات - Decore & More',
      user: req.user,
      layout: 'layouts/engineer'
    });
  }
);

/**
 * @route   GET /engineer/settings
 * @desc    Render engineer settings page
 * @access  Private (Engineer only)
 */
router.get('/settings',
  (req, res) => {
    res.render('engineer/settings', {
      title: 'الإعدادات - Decore & More',
      user: req.user,
      layout: 'layouts/engineer'
    });
  }
);

/**
 * @route   GET /engineer/reviews
 * @desc    Render engineer reviews page
 * @access  Private (Engineer only)
 */
router.get('/reviews',
  (req, res) => {
    res.render('engineer/reviews', {
      title: 'التقييمات - Decore & More',
      user: req.user,
      layout: 'layouts/engineer'
    });
  }
);

module.exports = router;
