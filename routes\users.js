/**
 * User Routes
 * Handles user management endpoints
 */

const express = require('express');
const router = express.Router();

// Controllers
const UserController = require('../controllers/userController');

// Middleware
const { 
  requireAuth, 
  requireAdmin, 
  requireEngineerOrAdmin,
  requireOwnershipOrAdmin 
} = require('../middleware/auth');
const { uploadConfigs } = require('../middleware/upload');
const { validatePagination } = require('../middleware/validation');

// Rate limiting
const rateLimit = require('express-rate-limit');

const userLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

// Apply rate limiting to all user routes
router.use(userLimiter);

/**
 * @route   GET /api/users/profile/:userId
 * @desc    Get user profile by ID
 * @access  Private (Own profile or Admin)
 */
router.get('/profile/:userId',
  requireAuth,
  requireOwnershipOrAdmin('userId'),
  UserController.getProfile
);

/**
 * @route   PUT /api/users/profile/:userId
 * @desc    Update user profile
 * @access  Private (Own profile or Admin)
 */
router.put('/profile/:userId',
  requireAuth,
  requireOwnershipOrAdmin('userId'),
  uploadConfigs.profileUpdate,
  UserController.updateProfile
);

/**
 * @route   DELETE /api/users/:userId
 * @desc    Delete user
 * @access  Private (Admin only)
 */
router.delete('/:userId',
  requireAuth,
  requireAdmin,
  UserController.deleteUser
);

/**
 * @route   GET /api/users/engineers
 * @desc    Get all engineers with filters
 * @access  Public
 */
router.get('/engineers',
  validatePagination,
  UserController.getEngineers
);

/**
 * @route   PUT /api/users/engineers/:engineerId/approve
 * @desc    Approve engineer
 * @access  Private (Admin only)
 */
router.put('/engineers/:engineerId/approve',
  requireAuth,
  requireAdmin,
  UserController.approveEngineer
);

/**
 * @route   GET /api/users/stats
 * @desc    Get user statistics
 * @access  Private (Admin only)
 */
router.get('/stats',
  requireAuth,
  requireAdmin,
  UserController.getUserStats
);

/**
 * @route   PUT /api/users/:userId/change-password
 * @desc    Change user password
 * @access  Private (Own account or Admin)
 */
router.put('/:userId/change-password',
  requireAuth,
  requireOwnershipOrAdmin('userId'),
  UserController.changePassword
);

/**
 * @route   GET /api/users/email/:email
 * @desc    Get user by email
 * @access  Private (Admin only)
 */
router.get('/email/:email',
  requireAuth,
  requireAdmin,
  UserController.getUserByEmail
);

// Web routes for user management pages

/**
 * @route   GET /users/profile
 * @desc    Render user profile page
 * @access  Private
 */
router.get('/profile',
  requireAuth,
  (req, res) => {
    res.render('user-profile', {
      title: 'الملف الشخصي - Decore & More',
      user: req.user
    });
  }
);

/**
 * @route   GET /users/edit-profile
 * @desc    Render edit profile page
 * @access  Private
 */
router.get('/edit-profile',
  requireAuth,
  (req, res) => {
    res.render('edit-profile', {
      title: 'تعديل الملف الشخصي - Decore & More',
      user: req.user
    });
  }
);

/**
 * @route   GET /users/engineers
 * @desc    Render engineers listing page
 * @access  Public
 */
router.get('/engineers',
  (req, res) => {
    res.render('engineers', {
      title: 'المهندسون - Decore & More',
      user: req.user || null
    });
  }
);

/**
 * @route   GET /users/engineer/:engineerId
 * @desc    Render engineer profile page
 * @access  Public
 */
router.get('/engineer/:engineerId',
  async (req, res, next) => {
    try {
      const { engineerId } = req.params;
      
      // Get engineer data (this would typically be done in a controller)
      const engineer = await UserController.getProfile(req, res, next);
      
      res.render('engineer-profile', {
        title: `${engineer.firstName} ${engineer.lastName} - Decore & More`,
        engineer,
        user: req.user || null
      });
    } catch (error) {
      next(error);
    }
  }
);

// Admin routes for user management

/**
 * @route   GET /admin/users
 * @desc    Render admin users management page
 * @access  Private (Admin only)
 */
router.get('/admin/users',
  requireAuth,
  requireAdmin,
  (req, res) => {
    res.render('admin/users', {
      title: 'إدارة المستخدمين - Decore & More',
      user: req.user
    });
  }
);

/**
 * @route   GET /admin/engineers
 * @desc    Render admin engineers management page
 * @access  Private (Admin only)
 */
router.get('/admin/engineers',
  requireAuth,
  requireAdmin,
  (req, res) => {
    res.render('admin/engineers', {
      title: 'إدارة المهندسين - Decore & More',
      user: req.user
    });
  }
);

/**
 * @route   GET /admin/engineers/pending
 * @desc    Render pending engineers approval page
 * @access  Private (Admin only)
 */
router.get('/admin/engineers/pending',
  requireAuth,
  requireAdmin,
  (req, res) => {
    res.render('admin/pending-engineers', {
      title: 'المهندسون المعلقون - Decore & More',
      user: req.user
    });
  }
);

// Engineer dashboard routes

/**
 * @route   GET /engineer/dashboard
 * @desc    Render engineer dashboard
 * @access  Private (Engineer only)
 */
router.get('/engineer/dashboard',
  requireAuth,
  requireEngineerOrAdmin,
  (req, res) => {
    res.render('engineer/dashboard', {
      title: 'لوحة التحكم - Decore & More',
      user: req.user
    });
  }
);

/**
 * @route   GET /engineer/profile
 * @desc    Render engineer profile management
 * @access  Private (Engineer only)
 */
router.get('/engineer/profile',
  requireAuth,
  requireEngineerOrAdmin,
  (req, res) => {
    res.render('engineer/profile', {
      title: 'إدارة الملف الشخصي - Decore & More',
      user: req.user
    });
  }
);

/**
 * @route   GET /engineer/settings
 * @desc    Render engineer settings page
 * @access  Private (Engineer only)
 */
router.get('/engineer/settings',
  requireAuth,
  requireEngineerOrAdmin,
  (req, res) => {
    res.render('engineer/settings', {
      title: 'الإعدادات - Decore & More',
      user: req.user
    });
  }
);

// Client dashboard routes

/**
 * @route   GET /client/dashboard
 * @desc    Render client dashboard
 * @access  Private (Client only)
 */
router.get('/client/dashboard',
  requireAuth,
  (req, res) => {
    // Only allow clients (users without specific roles or with 'User' role)
    if (req.user.role && req.user.role !== 'User' && req.user.role !== 'user') {
      return res.redirect('/dashboard');
    }
    
    res.render('client/dashboard', {
      title: 'لوحة التحكم - Decore & More',
      user: req.user
    });
  }
);

/**
 * @route   GET /client/bookings
 * @desc    Render client bookings page
 * @access  Private (Client only)
 */
router.get('/client/bookings',
  requireAuth,
  (req, res) => {
    if (req.user.role && req.user.role !== 'User' && req.user.role !== 'user') {
      return res.redirect('/dashboard');
    }
    
    res.render('client/bookings', {
      title: 'حجوزاتي - Decore & More',
      user: req.user
    });
  }
);

// Error handling middleware specific to user routes
router.use((err, req, res, next) => {
  // Log user-related errors
  logger.warn('User route error', {
    path: req.path,
    method: req.method,
    error: err.message,
    userId: req.user?.id,
    ip: req.ip
  });
  
  // Pass error to global error handler
  next(err);
});

module.exports = router;
