/**
 * Authentication Routes
 * Handles user authentication endpoints
 */

const express = require('express');
const router = express.Router();

// Controllers
const AuthController = require('../controllers/authController');

// Middleware
const { requireAuth, optionalAuth } = require('../middleware/auth');
const { uploadConfigs } = require('../middleware/upload');
const {
  validateUserRegistration,
  validateUserLogin,
  validatePasswordResetRequest,
  validatePasswordReset,
  validateEmailVerification
} = require('../middleware/validation');

// Rate limiting
const rateLimit = require('express-rate-limit');

// Rate limiters
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: 'Too many authentication attempts, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

const registrationLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // limit each IP to 3 registration attempts per hour
  message: 'Too many registration attempts, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

const passwordResetLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 3, // limit each IP to 3 password reset requests per 15 minutes
  message: 'Too many password reset attempts, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * @route   POST /api/auth/register
 * @desc    Register new user (Engineer/Admin)
 * @access  Public
 */
router.post('/register', 
  registrationLimiter,
  uploadConfigs.userRegistration,
  validateUserRegistration,
  AuthController.register
);

/**
 * @route   POST /api/auth/login
 * @desc    Login user
 * @access  Public
 */
router.post('/login',
  authLimiter,
  validateUserLogin,
  AuthController.login
);

/**
 * @route   POST /api/auth/logout
 * @desc    Logout user
 * @access  Private
 */
router.post('/logout',
  requireAuth,
  AuthController.logout
);

/**
 * @route   POST /api/auth/forgot-password
 * @desc    Request password reset
 * @access  Public
 */
router.post('/forgot-password',
  passwordResetLimiter,
  validatePasswordResetRequest,
  AuthController.forgotPassword
);

/**
 * @route   POST /api/auth/reset-password
 * @desc    Reset password with code
 * @access  Public
 */
router.post('/reset-password',
  passwordResetLimiter,
  validatePasswordReset,
  AuthController.resetPassword
);

/**
 * @route   POST /api/auth/verify-email
 * @desc    Verify email with code
 * @access  Public
 */
router.post('/verify-email',
  authLimiter,
  validateEmailVerification,
  AuthController.verifyEmail
);

/**
 * @route   POST /api/auth/resend-verification
 * @desc    Resend verification email
 * @access  Public
 */
router.post('/resend-verification',
  authLimiter,
  AuthController.resendVerification
);

/**
 * @route   GET /api/auth/me
 * @desc    Get current user info
 * @access  Private
 */
router.get('/me',
  requireAuth,
  AuthController.getCurrentUser
);

/**
 * @route   PUT /api/auth/change-password
 * @desc    Change user password
 * @access  Private
 */
router.put('/change-password',
  requireAuth,
  AuthController.changePassword
);

/**
 * @route   POST /api/auth/refresh-session
 * @desc    Refresh user session
 * @access  Private
 */
router.post('/refresh-session',
  requireAuth,
  AuthController.refreshSession
);

/**
 * @route   GET /api/auth/check-email
 * @desc    Check if email exists
 * @access  Public
 */
router.get('/check-email',
  AuthController.checkEmailExists
);

// Web routes (for rendering pages)

/**
 * @route   GET /login
 * @desc    Render login page
 * @access  Public
 */
router.get('/login',
  optionalAuth,
  (req, res) => {
    if (req.user) {
      // User already logged in, redirect based on role
      const redirectPath = req.user.role === 'Admin' ? '/admin/dashboard' : '/dashboard';
      return res.redirect(redirectPath);
    }
    res.render('login', { 
      title: 'تسجيل الدخول - Decore & More',
      error: req.query.error,
      message: req.query.message
    });
  }
);

/**
 * @route   GET /register
 * @desc    Render registration page
 * @access  Public
 */
router.get('/register',
  optionalAuth,
  (req, res) => {
    if (req.user) {
      const redirectPath = req.user.role === 'Admin' ? '/admin/dashboard' : '/dashboard';
      return res.redirect(redirectPath);
    }
    res.render('register', { 
      title: 'التسجيل - Decore & More',
      error: req.query.error,
      message: req.query.message
    });
  }
);

/**
 * @route   GET /forgot-password
 * @desc    Render forgot password page
 * @access  Public
 */
router.get('/forgot-password',
  optionalAuth,
  (req, res) => {
    if (req.user) {
      const redirectPath = req.user.role === 'Admin' ? '/admin/dashboard' : '/dashboard';
      return res.redirect(redirectPath);
    }
    res.render('forgot-password', { 
      title: 'نسيت كلمة المرور - Decore & More',
      error: req.query.error,
      message: req.query.message
    });
  }
);

/**
 * @route   GET /reset-password
 * @desc    Render reset password page
 * @access  Public
 */
router.get('/reset-password',
  optionalAuth,
  (req, res) => {
    if (req.user) {
      const redirectPath = req.user.role === 'Admin' ? '/admin/dashboard' : '/dashboard';
      return res.redirect(redirectPath);
    }
    res.render('reset-password', { 
      title: 'إعادة تعيين كلمة المرور - Decore & More',
      email: req.query.email,
      error: req.query.error,
      message: req.query.message
    });
  }
);

/**
 * @route   GET /verify-email
 * @desc    Render email verification page
 * @access  Public
 */
router.get('/verify-email',
  optionalAuth,
  (req, res) => {
    if (req.user && req.user.isVerified) {
      const redirectPath = req.user.role === 'Admin' ? '/admin/dashboard' : '/dashboard';
      return res.redirect(redirectPath);
    }
    res.render('verify-email', { 
      title: 'تأكيد البريد الإلكتروني - Decore & More',
      engineerId: req.query.engineerId,
      error: req.query.error,
      message: req.query.message
    });
  }
);

/**
 * @route   GET /dashboard
 * @desc    Render user dashboard (redirect based on role)
 * @access  Private
 */
router.get('/dashboard',
  requireAuth,
  (req, res) => {
    const user = req.user;
    
    switch (user.role) {
      case 'Admin':
        return res.redirect('/admin/dashboard');
      case 'Engineer':
        return res.redirect('/engineer/dashboard');
      default:
        return res.redirect('/client/dashboard');
    }
  }
);

// Error handling middleware specific to auth routes
router.use((err, req, res, next) => {
  // Log authentication errors
  if (err.name === 'AuthenticationError' || err.name === 'ValidationError') {
    logger.warn('Authentication route error', {
      path: req.path,
      method: req.method,
      error: err.message,
      ip: req.ip
    });
  }
  
  // Pass error to global error handler
  next(err);
});

module.exports = router;
